package com.example.loadtest;

import java.io.*;
import java.net.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;

/**
 * 标准HTTP负载测试工具
 * 使用Java标准库实现，支持高并发负载测试
 */
public class StandardLoadTester {
    
    // 配置参数
    private final String targetUrl;
    private final String httpMethod;
    private final int requestsPerSecond;
    private final int totalRequests;
    private final int durationSeconds;
    private final int concurrency;
    private final Map<String, String> headers;
    private final String requestBody;
    private final int connectionTimeout;
    private final int readTimeout;
    
    // 统计数据
    private final AtomicLong totalRequestsSent = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong connectionErrors = new AtomicLong(0);
    private final AtomicLong timeoutErrors = new AtomicLong(0);
    private final AtomicLong httpErrors = new AtomicLong(0);
    
    // 响应时间统计
    private final List<Long> responseTimes = Collections.synchronizedList(new ArrayList<>());
    
    // 控制变量
    private volatile boolean running = false;
    private final long startTime;
    private long endTime;
    
    // 线程池
    private ExecutorService requestExecutor;
    private ScheduledExecutorService scheduler;
    
    public StandardLoadTester(Builder builder) {
        this.targetUrl = builder.targetUrl;
        this.httpMethod = builder.httpMethod.toUpperCase();
        this.requestsPerSecond = builder.requestsPerSecond;
        this.totalRequests = builder.totalRequests;
        this.durationSeconds = builder.durationSeconds;
        this.concurrency = builder.concurrency;
        this.headers = new HashMap<>(builder.headers);
        this.requestBody = builder.requestBody;
        this.connectionTimeout = builder.connectionTimeout;
        this.readTimeout = builder.readTimeout;
        this.startTime = System.currentTimeMillis();
        
        // 设置默认headers
        if (!this.headers.containsKey("User-Agent")) {
            this.headers.put("User-Agent", "StandardLoadTester/1.0");
        }
    }
    
    public void startTest() {
        printTestHeader();
        
        running = true;
        
        // 创建线程池
        requestExecutor = Executors.newFixedThreadPool(concurrency);
        scheduler = Executors.newScheduledThreadPool(3);
        
        // 启动统计报告
        scheduler.scheduleAtFixedRate(this::printPeriodicStatistics, 5, 5, TimeUnit.SECONDS);
        
        // 启动请求发送器
        startRequestSender();
        
        // 如果设置了持续时间，安排停止
        if (durationSeconds > 0) {
            scheduler.schedule(this::stopTest, durationSeconds, TimeUnit.SECONDS);
        }
        
        // 等待测试完成
        waitForCompletion();
        
        // 清理资源
        cleanup();
        
        // 打印最终统计
        printFinalStatistics();
    }
    
    private void startRequestSender() {
        // 计算请求间隔（纳秒）
        long intervalNanos = TimeUnit.SECONDS.toNanos(1) / requestsPerSecond;
        
        scheduler.scheduleAtFixedRate(() -> {
            if (!running) return;
            
            // 检查是否达到总请求数限制
            if (totalRequests > 0 && totalRequestsSent.get() >= totalRequests) {
                stopTest();
                return;
            }
            
            // 提交请求任务
            requestExecutor.submit(this::sendRequest);
        }, 0, intervalNanos, TimeUnit.NANOSECONDS);
    }
    
    private void sendRequest() {
        if (!running) return;
        
        long requestStart = System.currentTimeMillis();
        totalRequestsSent.incrementAndGet();
        
        HttpURLConnection connection = null;
        try {
            URL url = new URL(targetUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // 配置连接
            connection.setRequestMethod(httpMethod);
            connection.setConnectTimeout(connectionTimeout);
            connection.setReadTimeout(readTimeout);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            
            // 设置请求头
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
            
            // 发送请求体
            if (!requestBody.isEmpty() && needsRequestBody(httpMethod)) {
                connection.setDoOutput(true);
                if (!headers.containsKey("Content-Type")) {
                    connection.setRequestProperty("Content-Type", "application/json");
                }

                // 处理动态值替换
                String processedBody = DynamicValueGenerator.processString(requestBody);

                try (OutputStream os = connection.getOutputStream()) {
                    os.write(processedBody.getBytes("UTF-8"));
                    os.flush();
                }
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            long responseTime = System.currentTimeMillis() - requestStart;
            
            // 读取响应体
            long bytesRead = readResponse(connection, responseCode);
            
            // 记录统计
            recordResponse(responseCode, responseTime, bytesRead);
            
        } catch (SocketTimeoutException e) {
            timeoutErrors.incrementAndGet();
            failedRequests.incrementAndGet();
            recordResponseTime(System.currentTimeMillis() - requestStart);
        } catch (ConnectException | UnknownHostException e) {
            connectionErrors.incrementAndGet();
            failedRequests.incrementAndGet();
            recordResponseTime(System.currentTimeMillis() - requestStart);
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            recordResponseTime(System.currentTimeMillis() - requestStart);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    private boolean needsRequestBody(String method) {
        return "POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method);
    }
    
    private long readResponse(HttpURLConnection connection, int responseCode) throws IOException {
        InputStream inputStream = null;
        try {
            inputStream = responseCode >= 200 && responseCode < 300 
                ? connection.getInputStream() 
                : connection.getErrorStream();
            
            if (inputStream == null) return 0;
            
            long bytesRead = 0;
            byte[] buffer = new byte[8192];
            int bytesReadThisTime;
            
            while ((bytesReadThisTime = inputStream.read(buffer)) != -1) {
                bytesRead += bytesReadThisTime;
            }
            
            return bytesRead;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
    
    private void recordResponse(int responseCode, long responseTime, long bytesRead) {
        recordResponseTime(responseTime);
        
        if (responseCode >= 200 && responseCode < 300) {
            successfulRequests.incrementAndGet();
            totalBytes.addAndGet(bytesRead);
        } else {
            httpErrors.incrementAndGet();
            failedRequests.incrementAndGet();
        }
    }
    
    private void recordResponseTime(long responseTime) {
        totalResponseTime.addAndGet(responseTime);
        responseTimes.add(responseTime);
    }
    
    private void stopTest() {
        if (running) {
            running = false;
            endTime = System.currentTimeMillis();
        }
    }
    
    private void waitForCompletion() {
        while (running) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                stopTest();
                break;
            }
        }
    }
    
    private void cleanup() {
        if (scheduler != null) {
            scheduler.shutdown();
        }
        
        if (requestExecutor != null) {
            requestExecutor.shutdown();
            try {
                if (!requestExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    requestExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                requestExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    private void printTestHeader() {
        String separator = repeatString("=", 80);
        System.out.println(separator);
        System.out.println("HTTP负载测试开始");
        System.out.println(separator);
        System.out.println("目标URL: " + targetUrl);
        System.out.println("HTTP方法: " + httpMethod);
        System.out.println("目标RPS: " + requestsPerSecond);
        System.out.println("并发数: " + concurrency);
        if (totalRequests > 0) {
            System.out.println("总请求数: " + totalRequests);
        } else {
            System.out.println("测试持续时间: " + durationSeconds + "秒");
        }
        System.out.println("连接超时: " + connectionTimeout + "ms");
        System.out.println("读取超时: " + readTimeout + "ms");
        System.out.println("开始时间: " + new Date());
        System.out.println(separator);
        System.out.println();
    }
    
    private void printPeriodicStatistics() {
        long currentTime = System.currentTimeMillis();
        long elapsedTime = (currentTime - startTime) / 1000;
        long total = totalRequestsSent.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        
        double currentRps = total > 0 ? (total * 1000.0) / (currentTime - startTime) : 0;
        double successRate = total > 0 ? (successful * 100.0) / total : 0;
        double avgResponseTime = total > 0 ? totalResponseTime.get() / (double) total : 0;
        
        System.out.printf("[%s] 运行时间: %ds | 总请求: %d | 成功: %d | 失败: %d | 平均RPS: %.1f%n",
            new Date(), elapsedTime, total, successful, failed, currentRps);
        System.out.printf("    成功率: %.2f%% | 平均响应时间: %.1fms | 数据传输: %.2fMB%n",
            successRate, avgResponseTime, totalBytes.get() / 1024.0 / 1024.0);
        
        if (failed > 0) {
            System.out.printf("    错误统计 - 连接: %d | 超时: %d | HTTP: %d%n",
                connectionErrors.get(), timeoutErrors.get(), httpErrors.get());
        }
        
        System.out.println();
    }
    
    private void printFinalStatistics() {
        System.out.println();
        String separator = repeatString("=", 80);
        System.out.println(separator);
        System.out.println("负载测试完成 - 最终统计报告");
        System.out.println(separator);
        
        long totalTime = endTime - startTime;
        long total = totalRequestsSent.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        
        double overallRps = total > 0 ? (total * 1000.0) / totalTime : 0;
        double successRate = total > 0 ? (successful * 100.0) / total : 0;
        double avgResponseTime = total > 0 ? totalResponseTime.get() / (double) total : 0;
        
        System.out.println("测试概要:");
        System.out.printf("  总运行时间: %.1f秒%n", totalTime / 1000.0);
        System.out.printf("  总请求数: %d%n", total);
        System.out.printf("  成功请求: %d%n", successful);
        System.out.printf("  失败请求: %d%n", failed);
        System.out.printf("  成功率: %.2f%%%n", successRate);
        System.out.printf("  平均RPS: %.2f%n", overallRps);
        System.out.printf("  平均响应时间: %.1fms%n", avgResponseTime);
        System.out.printf("  数据传输总量: %.2fMB%n", totalBytes.get() / 1024.0 / 1024.0);
        
        // 响应时间统计
        printResponseTimeStatistics();
        
        // 错误统计
        if (failed > 0) {
            System.out.println();
            System.out.println("错误统计:");
            System.out.printf("  连接错误: %d%n", connectionErrors.get());
            System.out.printf("  超时错误: %d%n", timeoutErrors.get());
            System.out.printf("  HTTP错误: %d%n", httpErrors.get());
        }
        
        System.out.println(separator);
    }
    
    private void printResponseTimeStatistics() {
        if (responseTimes.isEmpty()) return;
        
        List<Long> sortedTimes = new ArrayList<>(responseTimes);
        Collections.sort(sortedTimes);
        
        System.out.println();
        System.out.println("响应时间统计:");
        System.out.printf("  最小值: %dms%n", sortedTimes.get(0));
        System.out.printf("  最大值: %dms%n", sortedTimes.get(sortedTimes.size() - 1));
        System.out.printf("  P50 (中位数): %dms%n", getPercentile(sortedTimes, 50));
        System.out.printf("  P90: %dms%n", getPercentile(sortedTimes, 90));
        System.out.printf("  P95: %dms%n", getPercentile(sortedTimes, 95));
        System.out.printf("  P99: %dms%n", getPercentile(sortedTimes, 99));
        System.out.printf("  P99.9: %dms%n", getPercentile(sortedTimes, 99.9));
    }
    
    private long getPercentile(List<Long> sortedList, double percentile) {
        if (sortedList.isEmpty()) return 0;
        int index = (int) Math.ceil(percentile / 100.0 * sortedList.size()) - 1;
        index = Math.max(0, Math.min(index, sortedList.size() - 1));
        return sortedList.get(index);
    }
    
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    // Builder模式
    public static class Builder {
        private String targetUrl;
        private String httpMethod = "GET";
        private int requestsPerSecond = 100;
        private int totalRequests = -1;
        private int durationSeconds = 60;
        private int concurrency = 10;
        private Map<String, String> headers = new HashMap<>();
        private String requestBody = "";
        private int connectionTimeout = 30000;
        private int readTimeout = 30000;
        
        public Builder url(String url) {
            this.targetUrl = url;
            return this;
        }
        
        public Builder method(String method) {
            this.httpMethod = method;
            return this;
        }
        
        public Builder rps(int rps) {
            this.requestsPerSecond = rps;
            return this;
        }
        
        public Builder requests(int requests) {
            this.totalRequests = requests;
            return this;
        }
        
        public Builder duration(int seconds) {
            this.durationSeconds = seconds;
            return this;
        }
        
        public Builder concurrency(int concurrency) {
            this.concurrency = concurrency;
            return this;
        }
        
        public Builder header(String key, String value) {
            this.headers.put(key, value);
            return this;
        }
        
        public Builder body(String body) {
            this.requestBody = body;
            return this;
        }
        
        public Builder connectionTimeout(int timeout) {
            this.connectionTimeout = timeout;
            return this;
        }
        
        public Builder readTimeout(int timeout) {
            this.readTimeout = timeout;
            return this;
        }
        
        public StandardLoadTester build() {
            if (targetUrl == null || targetUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("目标URL不能为空");
            }
            return new StandardLoadTester(this);
        }

        // Getter方法用于显示配置摘要
        public String getTargetUrl() { return targetUrl; }
        public String getHttpMethod() { return httpMethod; }
        public int getRequestsPerSecond() { return requestsPerSecond; }
        public int getTotalRequests() { return totalRequests; }
        public int getDurationSeconds() { return durationSeconds; }
        public int getConcurrency() { return concurrency; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }
        public String getRequestBody() { return requestBody; }
        public int getConnectionTimeout() { return connectionTimeout; }
        public int getReadTimeout() { return readTimeout; }
    }
}
