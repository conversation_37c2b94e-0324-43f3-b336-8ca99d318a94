package com.example.loadtest;

import java.util.UUID;
import java.util.Random;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 动态值生成器
 * 支持在请求体中使用特殊标识符来生成动态值
 */
public class DynamicValueGenerator {
    
    private static final Random random = new Random();
    
    // 支持的动态值模式
    private static final Pattern UUID_PATTERN = Pattern.compile("\\{\\{UUID\\}\\}");
    private static final Pattern TIMESTAMP_PATTERN = Pattern.compile("\\{\\{TIMESTAMP\\}\\}");
    private static final Pattern RANDOM_INT_PATTERN = Pattern.compile("\\{\\{RANDOM_INT\\}\\}");
    private static final Pattern RANDOM_INT_RANGE_PATTERN = Pattern.compile("\\{\\{RANDOM_INT\\((\\d+),(\\d+)\\)\\}\\}");
    private static final Pattern RANDOM_STRING_PATTERN = Pattern.compile("\\{\\{RANDOM_STRING\\}\\}");
    private static final Pattern RANDOM_STRING_LENGTH_PATTERN = Pattern.compile("\\{\\{RANDOM_STRING\\((\\d+)\\)\\}\\}");
    private static final Pattern CURRENT_TIME_PATTERN = Pattern.compile("\\{\\{CURRENT_TIME\\}\\}");
    private static final Pattern EPOCH_TIME_PATTERN = Pattern.compile("\\{\\{EPOCH_TIME\\}\\}");
    
    /**
     * 处理字符串中的动态值标识符，替换为实际生成的值
     * 
     * @param input 包含动态值标识符的字符串
     * @return 替换后的字符串
     */
    public static String processString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        String result = input;
        
        // 替换UUID
        result = UUID_PATTERN.matcher(result).replaceAll(matchResult -> generateUUID());
        
        // 替换时间戳
        result = TIMESTAMP_PATTERN.matcher(result).replaceAll(matchResult -> generateTimestamp());
        
        // 替换当前时间
        result = CURRENT_TIME_PATTERN.matcher(result).replaceAll(matchResult -> generateCurrentTime());
        
        // 替换Epoch时间
        result = EPOCH_TIME_PATTERN.matcher(result).replaceAll(matchResult -> generateEpochTime());
        
        // 替换随机整数（带范围）
        Matcher rangeIntMatcher = RANDOM_INT_RANGE_PATTERN.matcher(result);
        while (rangeIntMatcher.find()) {
            int min = Integer.parseInt(rangeIntMatcher.group(1));
            int max = Integer.parseInt(rangeIntMatcher.group(2));
            String replacement = String.valueOf(generateRandomInt(min, max));
            result = result.replace(rangeIntMatcher.group(), replacement);
        }
        
        // 替换随机整数（默认范围）
        result = RANDOM_INT_PATTERN.matcher(result).replaceAll(matchResult -> generateRandomInt());
        
        // 替换随机字符串（指定长度）
        Matcher lengthStringMatcher = RANDOM_STRING_LENGTH_PATTERN.matcher(result);
        while (lengthStringMatcher.find()) {
            int length = Integer.parseInt(lengthStringMatcher.group(1));
            String replacement = generateRandomString(length);
            result = result.replace(lengthStringMatcher.group(), replacement);
        }
        
        // 替换随机字符串（默认长度）
        result = RANDOM_STRING_PATTERN.matcher(result).replaceAll(matchResult -> generateRandomString());
        
        return result;
    }
    
    /**
     * 生成UUID
     */
    private static String generateUUID() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 生成时间戳（毫秒）
     */
    private static String generateTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }
    
    /**
     * 生成当前时间（ISO格式）
     */
    private static String generateCurrentTime() {
        return java.time.Instant.now().toString();
    }
    
    /**
     * 生成Epoch时间（秒）
     */
    private static String generateEpochTime() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }
    
    /**
     * 生成随机整数（默认范围 1-1000）
     */
    private static String generateRandomInt() {
        return String.valueOf(random.nextInt(1000) + 1);
    }
    
    /**
     * 生成指定范围的随机整数
     */
    private static int generateRandomInt(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }
    
    /**
     * 生成随机字符串（默认长度8）
     */
    private static String generateRandomString() {
        return generateRandomString(8);
    }
    
    /**
     * 生成指定长度的随机字符串
     */
    private static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
    
    /**
     * 检查字符串是否包含动态值标识符
     */
    public static boolean containsDynamicValues(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        
        return UUID_PATTERN.matcher(input).find() ||
               TIMESTAMP_PATTERN.matcher(input).find() ||
               RANDOM_INT_PATTERN.matcher(input).find() ||
               RANDOM_INT_RANGE_PATTERN.matcher(input).find() ||
               RANDOM_STRING_PATTERN.matcher(input).find() ||
               RANDOM_STRING_LENGTH_PATTERN.matcher(input).find() ||
               CURRENT_TIME_PATTERN.matcher(input).find() ||
               EPOCH_TIME_PATTERN.matcher(input).find();
    }
    
    /**
     * 获取支持的动态值说明
     */
    public static String getSupportedDynamicValues() {
        return "支持的动态值标识符:\n" +
               "  {{UUID}} - 生成UUID (如: 123e4567-e89b-12d3-a456-************)\n" +
               "  {{TIMESTAMP}} - 生成时间戳毫秒 (如: 1640995200000)\n" +
               "  {{CURRENT_TIME}} - 生成ISO时间 (如: 2021-12-31T16:00:00Z)\n" +
               "  {{EPOCH_TIME}} - 生成Epoch秒 (如: 1640995200)\n" +
               "  {{RANDOM_INT}} - 生成随机整数1-1000 (如: 456)\n" +
               "  {{RANDOM_INT(1,100)}} - 生成指定范围随机整数 (如: 23)\n" +
               "  {{RANDOM_STRING}} - 生成8位随机字符串 (如: aB3xY9mK)\n" +
               "  {{RANDOM_STRING(12)}} - 生成指定长度随机字符串 (如: aB3xY9mKpQ2r)";
    }
    
    /**
     * 获取示例JSON
     */
    public static String getExampleJson() {
        return "{\n" +
               "  \"eventCode\": \"fin_test\",\n" +
               "  \"uid\": \"{{UUID}}\",\n" +
               "  \"requestId\": \"{{RANDOM_STRING(20)}}\",\n" +
               "  \"timestamp\": {{TIMESTAMP}},\n" +
               "  \"params\": {\n" +
               "    \"userKey\": \"test_{{RANDOM_STRING(30)}}\",\n" +
               "    \"amount\": {{RANDOM_INT(1,10000)}}\n" +
               "  },\n" +
               "  \"userKey\": \"test_{{RANDOM_STRING(30)}}\"\n" +
               "}";
    }
}
