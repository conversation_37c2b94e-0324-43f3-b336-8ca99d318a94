package com.example.loadtest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * 交互式HTTP负载测试工具
 * 通过用户交互方式收集测试参数，然后执行负载测试
 */
public class InteractiveLoadTester {
    
    private static final String VERSION = "1.0.0";
    private static final BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
    
    public static void main(String[] args) {
        try {
            printWelcome();

            // 选择配置模式
            StandardLoadTester.Builder builder = chooseConfigurationMode();

            // 显示配置摘要
            showConfigurationSummary(builder);
            
            // 确认执行
            if (confirmExecution()) {
                // 构建并启动测试
                StandardLoadTester tester = builder.build();
                
                // 注册关闭钩子
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    System.out.println("\n收到关闭信号，正在停止测试...");
                }));
                
                System.out.println("\n正在启动负载测试...");
                tester.startTest();
                
                System.out.println("\n测试完成！感谢使用HTTP负载测试工具。");
            } else {
                System.out.println("测试已取消。");
            }
            
        } catch (Exception e) {
            System.err.println("\n错误: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * 打印欢迎信息
     */
    private static void printWelcome() {
        String separator = repeatString("=", 80);
        System.out.println(separator);
        System.out.println("                    HTTP负载测试工具 v" + VERSION);
        System.out.println("                  交互式配置向导 - 简单易用");
        System.out.println(separator);
        System.out.println("欢迎使用HTTP负载测试工具！");
        System.out.println("本工具将引导您配置负载测试参数，支持每分钟100,000+请求。");
        System.out.println("提示：直接按回车键将使用默认值，输入 'help' 查看参数说明。");
        System.out.println(separator);
        System.out.println();
    }
    
    /**
     * 选择配置模式
     */
    private static StandardLoadTester.Builder chooseConfigurationMode() throws IOException {
        System.out.println("请选择配置模式:");
        System.out.println("1. 快速配置 (使用预设模板)");
        System.out.println("2. 自定义配置 (逐项设置参数)");
        System.out.println();

        while (true) {
            System.out.print("请选择 (1 或 2): ");
            String choice = reader.readLine().trim();

            if ("1".equals(choice)) {
                return chooseTemplate();
            } else if ("2".equals(choice)) {
                return collectConfiguration();
            } else {
                System.out.println("请输入 1 或 2");
            }
        }
    }

    /**
     * 选择预设模板
     */
    private static StandardLoadTester.Builder chooseTemplate() throws IOException {
        System.out.println();
        System.out.println("📋 选择测试模板:");
        System.out.println("1. 轻量测试 (10 RPS, 5并发, 30秒) - 适合初次测试");
        System.out.println("2. 中等负载 (100 RPS, 20并发, 60秒) - 适合常规测试");
        System.out.println("3. 高负载测试 (500 RPS, 50并发, 120秒) - 适合压力测试");
        System.out.println("4. 极限测试 (1000 RPS, 100并发, 300秒) - 适合性能极限测试");
        System.out.println("5. API接口测试 (POST请求模板)");
        System.out.println();

        while (true) {
            System.out.print("请选择模板 (1-5): ");
            String choice = reader.readLine().trim();

            // 首先获取URL
            String url = promptForInput(
                "目标URL",
                "要测试的HTTP服务地址",
                "https://httpbin.org/get",
                null,
                true
            );

            StandardLoadTester.Builder builder = new StandardLoadTester.Builder().url(url);

            switch (choice) {
                case "1":
                    return builder.rps(10).concurrency(5).duration(30);
                case "2":
                    return builder.rps(100).concurrency(20).duration(60);
                case "3":
                    return builder.rps(500).concurrency(50).duration(120);
                case "4":
                    return builder.rps(1000).concurrency(100).duration(300);
                case "5":
                    return setupApiTemplate(builder);
                default:
                    System.out.println("请输入 1-5 之间的数字");
            }
        }
    }

    /**
     * 设置API接口测试模板
     */
    private static StandardLoadTester.Builder setupApiTemplate(StandardLoadTester.Builder builder) throws IOException {
        System.out.println();
        System.out.println("🔧 API接口测试配置:");

        // HTTP方法
        String method = promptForInput(
            "HTTP方法",
            "API接口的请求方法",
            "POST",
            "POST",
            false
        );
        builder.method(method);

        // 请求体
        if (needsRequestBody(method)) {
            String body = promptForInputWithDynamicValues(
                "请求体 (JSON)",
                "API接口的请求数据，支持动态值生成",
                DynamicValueGenerator.getExampleJson(),
                "{\"test\": \"data\"}",
                false
            );
            builder.body(body);
            builder.header("Content-Type", "application/json");
        }

        // 认证头 (可选)
        String authHeader = promptForInput(
            "认证头 (可选)",
            "如需要认证，请输入Authorization头的值",
            "Bearer your-token-here",
            "",
            false
        );
        if (!authHeader.isEmpty()) {
            builder.header("Authorization", authHeader);
        }

        // 负载级别
        String loadLevel = promptForInput(
            "负载级别",
            "选择测试强度: light(轻量), medium(中等), heavy(重载)",
            "medium",
            "medium",
            false
        );

        switch (loadLevel.toLowerCase()) {
            case "light":
                return builder.rps(50).concurrency(10).duration(60);
            case "heavy":
                return builder.rps(300).concurrency(30).duration(180);
            default: // medium
                return builder.rps(100).concurrency(20).duration(120);
        }
    }

    /**
     * 交互式收集配置
     */
    private static StandardLoadTester.Builder collectConfiguration() throws IOException {
        StandardLoadTester.Builder builder = new StandardLoadTester.Builder();
        
        // 1. 目标URL (必需)
        String url = promptForInput(
            "目标URL",
            "要测试的HTTP服务地址",
            "https://httpbin.org/get",
            null,
            true
        );
        builder.url(url);
        
        // 2. HTTP方法
        String method = promptForInput(
            "HTTP方法",
            "请求方法类型",
            "GET",
            "GET",
            false
        );
        builder.method(method);
        
        // 3. 每秒请求数
        String rpsStr = promptForInput(
            "每秒请求数 (RPS)",
            "控制负载强度，建议从小开始测试",
            "100",
            "100",
            false
        );
        builder.rps(parseInteger(rpsStr, 100));
        
        // 4. 测试持续时间
        String durationStr = promptForInput(
            "测试持续时间 (秒)",
            "测试运行的总时间",
            "60",
            "60",
            false
        );
        builder.duration(parseInteger(durationStr, 60));
        
        // 5. 并发数
        String concurrencyStr = promptForInput(
            "并发连接数",
            "同时发送请求的线程数，影响并发性能",
            "20",
            "20",
            false
        );
        builder.concurrency(parseInteger(concurrencyStr, 20));
        
        // 6. 高级配置选项
        System.out.println("\n" + repeatString("-", 60));
        System.out.println("高级配置 (可选，直接回车跳过)");
        System.out.println(repeatString("-", 60));
        
        // 6.1 请求体 (用于POST/PUT等)
        if (needsRequestBody(method)) {
            String body = promptForInput(
                "请求体内容",
                "JSON格式的请求数据",
                "{\"test\": \"data\"}",
                "",
                false
            );
            if (!body.isEmpty()) {
                builder.body(body);
            }
        }
        
        // 6.2 自定义请求头
        List<String> headers = promptForHeaders();
        for (String header : headers) {
            String[] parts = header.split(":", 2);
            if (parts.length == 2) {
                builder.header(parts[0].trim(), parts[1].trim());
            }
        }
        
        // 6.3 连接超时
        String connectTimeoutStr = promptForInput(
            "连接超时时间 (秒)",
            "建立连接的最大等待时间",
            "30",
            "30",
            false
        );
        builder.connectionTimeout(parseInteger(connectTimeoutStr, 30) * 1000);
        
        // 6.4 读取超时
        String readTimeoutStr = promptForInput(
            "读取超时时间 (秒)",
            "等待响应数据的最大时间",
            "30",
            "30",
            false
        );
        builder.readTimeout(parseInteger(readTimeoutStr, 30) * 1000);
        
        return builder;
    }
    
    /**
     * 提示用户输入参数
     */
    private static String promptForInput(String paramName, String description, 
                                       String example, String defaultValue, 
                                       boolean required) throws IOException {
        System.out.println();
        System.out.println("📋 " + paramName);
        System.out.println("   说明: " + description);
        System.out.println("   示例: " + example);
        if (defaultValue != null) {
            System.out.println("   默认: " + defaultValue);
        }
        
        while (true) {
            System.out.print("   请输入: ");
            String input = reader.readLine().trim();
            
            if ("help".equalsIgnoreCase(input)) {
                showParameterHelp(paramName);
                continue;
            }
            
            if (input.isEmpty()) {
                if (required && defaultValue == null) {
                    System.out.println("   ❌ 此参数为必需项，请输入有效值");
                    continue;
                }
                return defaultValue != null ? defaultValue : "";
            }
            
            return input;
        }
    }
    
    /**
     * 提示用户输入请求头
     */
    private static List<String> promptForHeaders() throws IOException {
        List<String> headers = new ArrayList<>();
        
        System.out.println();
        System.out.println("📋 自定义请求头 (可选)");
        System.out.println("   说明: 添加HTTP请求头，如认证信息、内容类型等");
        System.out.println("   格式: Key: Value");
        System.out.println("   示例: Authorization: Bearer token123");
        System.out.println("   示例: Content-Type: application/json");
        System.out.println("   提示: 直接回车完成输入，输入多个请求头请逐行输入");
        
        while (true) {
            System.out.print("   请输入请求头 (回车结束): ");
            String input = reader.readLine().trim();
            
            if (input.isEmpty()) {
                break;
            }
            
            if (input.contains(":")) {
                headers.add(input);
                System.out.println("   ✅ 已添加: " + input);
            } else {
                System.out.println("   ❌ 格式错误，请使用 'Key: Value' 格式");
            }
        }
        
        return headers;
    }
    
    /**
     * 显示配置摘要
     */
    private static void showConfigurationSummary(StandardLoadTester.Builder builder) {
        System.out.println();
        String separator = repeatString("=", 60);
        System.out.println(separator);
        System.out.println("                    配置摘要");
        System.out.println(separator);

        System.out.println("🎯 基本配置:");
        System.out.println("   目标URL: " + builder.getTargetUrl());
        System.out.println("   HTTP方法: " + builder.getHttpMethod());
        System.out.println("   每秒请求数: " + builder.getRequestsPerSecond() + " RPS");
        System.out.println("   测试持续时间: " + builder.getDurationSeconds() + " 秒");
        System.out.println("   并发连接数: " + builder.getConcurrency());

        System.out.println();
        System.out.println("⚙️  高级配置:");
        System.out.println("   连接超时: " + (builder.getConnectionTimeout() / 1000) + " 秒");
        System.out.println("   读取超时: " + (builder.getReadTimeout() / 1000) + " 秒");

        if (!builder.getRequestBody().isEmpty()) {
            System.out.println("   请求体: " +
                (builder.getRequestBody().length() > 50 ?
                 builder.getRequestBody().substring(0, 50) + "..." :
                 builder.getRequestBody()));
        }

        if (!builder.getHeaders().isEmpty()) {
            System.out.println("   自定义请求头: " + builder.getHeaders().size() + " 个");
            for (String key : builder.getHeaders().keySet()) {
                System.out.println("     " + key + ": " + builder.getHeaders().get(key));
            }
        }

        System.out.println();
        System.out.println("📊 预计负载:");
        int totalRequests = builder.getRequestsPerSecond() * builder.getDurationSeconds();
        System.out.println("   总请求数: " + totalRequests + " 个");
        System.out.println("   平均每秒: " + builder.getRequestsPerSecond() + " 请求");
        System.out.println("   峰值并发: " + builder.getConcurrency() + " 连接");

        System.out.println();
        System.out.println("⚠️  重要提醒:");
        System.out.println("   • 请确保您有权限对目标服务器进行负载测试");
        System.out.println("   • 建议先进行小规模测试验证配置正确性");
        System.out.println("   • 监控目标服务器资源使用情况");
        System.out.println(separator);
    }
    
    /**
     * 确认执行
     */
    private static boolean confirmExecution() throws IOException {
        System.out.println();
        System.out.println("🚀 准备开始负载测试");
        System.out.println("⚠️  警告：负载测试可能对目标服务器产生较大压力");
        System.out.println("   请确保您有权限对目标服务器进行测试");
        System.out.println();
        
        while (true) {
            System.out.print("确认开始测试吗？(y/n): ");
            String input = reader.readLine().trim().toLowerCase();
            
            if ("y".equals(input) || "yes".equals(input)) {
                return true;
            } else if ("n".equals(input) || "no".equals(input)) {
                return false;
            } else {
                System.out.println("请输入 'y' 或 'n'");
            }
        }
    }
    
    /**
     * 显示参数帮助信息
     */
    private static void showParameterHelp(String paramName) {
        System.out.println();
        System.out.println("📖 " + paramName + " 详细说明:");
        
        switch (paramName) {
            case "目标URL":
                System.out.println("   • 要测试的HTTP服务完整地址");
                System.out.println("   • 必须包含协议 (http:// 或 https://)");
                System.out.println("   • 示例: https://api.example.com/users");
                System.out.println("   • 示例: http://localhost:8080/test");
                break;
                
            case "HTTP方法":
                System.out.println("   • 支持的方法: GET, POST, PUT, DELETE, HEAD, PATCH");
                System.out.println("   • GET: 获取数据，最常用的测试方法");
                System.out.println("   • POST: 提交数据，需要配置请求体");
                System.out.println("   • PUT: 更新数据，需要配置请求体");
                break;
                
            case "每秒请求数 (RPS)":
                System.out.println("   • 控制负载测试的强度");
                System.out.println("   • 建议从较小值开始 (如100)，逐步增加");
                System.out.println("   • 高RPS需要足够的并发数支持");
                System.out.println("   • 最大支持: 100,000+ 每分钟");
                break;
                
            case "并发连接数":
                System.out.println("   • 同时发送请求的线程数");
                System.out.println("   • 影响系统资源使用和并发性能");
                System.out.println("   • 一般设置为 RPS/10 到 RPS/5 之间");
                System.out.println("   • 过高可能导致系统资源不足");
                break;
                
            case "测试持续时间 (秒)":
                System.out.println("   • 负载测试运行的总时间");
                System.out.println("   • 建议至少60秒以获得稳定的统计数据");
                System.out.println("   • 长时间测试可以发现性能衰减问题");
                break;
                
            default:
                System.out.println("   • 暂无详细说明");
        }
        System.out.println();
    }
    
    /**
     * 判断HTTP方法是否需要请求体
     */
    private static boolean needsRequestBody(String method) {
        return "POST".equalsIgnoreCase(method) || 
               "PUT".equalsIgnoreCase(method) || 
               "PATCH".equalsIgnoreCase(method);
    }
    
    /**
     * 解析整数，失败时返回默认值
     */
    private static int parseInteger(String str, int defaultValue) {
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 重复字符串
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
