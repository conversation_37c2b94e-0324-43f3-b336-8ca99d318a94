package com.example.loadtest;

/**
 * 动态值生成器测试类
 * 用于验证动态值生成功能
 */
public class DynamicValueTest {
    
    public static void main(String[] args) {
        System.out.println("=== 动态值生成器测试 ===\n");
        
        // 测试您提供的JSON数据
        String testJson = "{\n" +
            "  \"eventCode\": \"fin_test\",\n" +
            "  \"uid\": \"{{UUID}}\",\n" +
            "  \"requestId\": \"{{RANDOM_STRING(20)}}\",\n" +
            "  \"params\": {\n" +
            "    \"userKey\": \"test_{{RANDOM_STRING(30)}}\"\n" +
            "  },\n" +
            "  \"userKey\": \"test_{{RANDOM_STRING(30)}}\",\n" +
            "  \"timestamp\": {{TIMESTAMP}}\n" +
            "}";
        
        System.out.println("原始JSON:");
        System.out.println(testJson);
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 生成3次，展示每次都不同
        for (int i = 1; i <= 3; i++) {
            System.out.println("第" + i + "次生成结果:");
            String processed = DynamicValueGenerator.processString(testJson);
            System.out.println(processed);
            System.out.println("\n" + "-".repeat(30) + "\n");
            
            // 稍微延迟以确保时间戳不同
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 测试所有支持的动态值类型
        System.out.println("=== 所有动态值类型测试 ===\n");
        
        String allTypesTest = "{\n" +
            "  \"uuid\": \"{{UUID}}\",\n" +
            "  \"timestamp\": {{TIMESTAMP}},\n" +
            "  \"currentTime\": \"{{CURRENT_TIME}}\",\n" +
            "  \"epochTime\": {{EPOCH_TIME}},\n" +
            "  \"randomInt\": {{RANDOM_INT}},\n" +
            "  \"randomIntRange\": {{RANDOM_INT(100,999)}},\n" +
            "  \"randomString\": \"{{RANDOM_STRING}}\",\n" +
            "  \"randomStringLength\": \"{{RANDOM_STRING(15)}}\"\n" +
            "}";
        
        System.out.println("所有类型测试模板:");
        System.out.println(allTypesTest);
        System.out.println("\n生成结果:");
        System.out.println(DynamicValueGenerator.processString(allTypesTest));
        
        // 测试检测功能
        System.out.println("\n=== 动态值检测测试 ===");
        System.out.println("包含动态值: " + DynamicValueGenerator.containsDynamicValues(testJson));
        System.out.println("不包含动态值: " + DynamicValueGenerator.containsDynamicValues("{\"test\": \"static\"}"));
        
        System.out.println("\n=== 支持的动态值说明 ===");
        System.out.println(DynamicValueGenerator.getSupportedDynamicValues());
    }
}
